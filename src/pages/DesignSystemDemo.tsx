import React, { useState } from 'react';
import { 
  ChefHat, 
  Heart, 
  Clock, 
  Users, 
  Star, 
  Bookmark, 
  ShoppingCart,
  Plus,
  Settings,
  Search,
  Filter
} from 'lucide-react';
import { Button, ButtonGroup, IconButton } from '@/components/design-system/atoms/Button';
import { 
  <PERSON>, 
  CardHeader, 
  CardTitle, 
  CardDescription, 
  CardContent, 
  CardFooter,
  CardImage 
} from '@/components/design-system/atoms/Card';

const DesignSystemDemo: React.FC = () => {
  const [likedRecipes, setLikedRecipes] = useState<Set<number>>(new Set());
  const [loading, setLoading] = useState<number | null>(null);

  const toggleLike = (recipeId: number) => {
    setLoading(recipeId);
    setTimeout(() => {
      setLikedRecipes(prev => {
        const newSet = new Set(prev);
        if (newSet.has(recipeId)) {
          newSet.delete(recipeId);
        } else {
          newSet.add(recipeId);
        }
        return newSet;
      });
      setLoading(null);
    }, 1000);
  };

  const sampleRecipes = [
    {
      id: 1,
      title: "Creamy Pasta Carbonara",
      description: "Classic Italian pasta with eggs, cheese, and pancetta",
      image: "https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400&h=300&fit=crop",
      time: "25 min",
      servings: 4,
      rating: 4.8,
    },
    {
      id: 2,
      title: "Grilled Salmon Teriyaki",
      description: "Fresh salmon glazed with homemade teriyaki sauce",
      image: "https://images.unsplash.com/photo-1467003909585-2f8a72700288?w=400&h=300&fit=crop",
      time: "20 min",
      servings: 2,
      rating: 4.6,
    },
    {
      id: 3,
      title: "Vegetable Stir Fry",
      description: "Colorful mix of fresh vegetables in savory sauce",
      image: "https://images.unsplash.com/photo-1512621776951-a57141f2eefd?w=400&h=300&fit=crop",
      time: "15 min",
      servings: 3,
      rating: 4.4,
    },
    {
      id: 4,
      title: "Chocolate Lava Cake",
      description: "Decadent dessert with molten chocolate center",
      image: "https://images.unsplash.com/photo-1606313564200-e75d5e30476c?w=400&h=300&fit=crop",
      time: "35 min",
      servings: 6,
      rating: 4.9,
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-7xl">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">
            Design System Demo
          </h1>
          <p className="text-lg text-gray-600">
            Showcase of enhanced UI components with BMAD-METHOD integration
          </p>
        </div>

        {/* Button Showcase */}
        <section className="mb-12">
          <Card className="p-6">
            <h2 className="text-2xl font-semibold mb-6">Enhanced Buttons</h2>
            
            {/* Button Variants */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Variants</h3>
              <div className="flex flex-wrap gap-3">
                <Button variant="primary">Primary</Button>
                <Button variant="secondary">Secondary</Button>
                <Button variant="ghost">Ghost</Button>
                <Button variant="danger">Danger</Button>
                <Button variant="outline">Outline</Button>
                <Button variant="link">Link</Button>
              </div>
            </div>

            {/* Button Sizes */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Sizes</h3>
              <div className="flex flex-wrap items-center gap-3">
                <Button size="xs">Extra Small</Button>
                <Button size="sm">Small</Button>
                <Button size="md">Medium</Button>
                <Button size="lg">Large</Button>
                <Button size="xl">Extra Large</Button>
              </div>
            </div>

            {/* Button States */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">States & Features</h3>
              <div className="flex flex-wrap gap-3">
                <Button loading loadingText="Saving...">
                  Loading Button
                </Button>
                <Button disabled>Disabled</Button>
                <Button leftIcon={<ChefHat className="w-4 h-4" />}>
                  With Icon
                </Button>
                <Button 
                  rightIcon={<ShoppingCart className="w-4 h-4" />}
                  variant="outline"
                >
                  Add to Cart
                </Button>
              </div>
            </div>

            {/* Icon Buttons */}
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Icon Buttons</h3>
              <div className="flex gap-3">
                <IconButton
                  icon={<Heart className="w-4 h-4" />}
                  aria-label="Like"
                  variant="ghost"
                />
                <IconButton
                  icon={<Bookmark className="w-4 h-4" />}
                  aria-label="Bookmark"
                  variant="outline"
                />
                <IconButton
                  icon={<Settings className="w-4 h-4" />}
                  aria-label="Settings"
                  variant="primary"
                />
              </div>
            </div>

            {/* Button Groups */}
            <div>
              <h3 className="text-lg font-medium mb-3">Button Groups</h3>
              <div className="space-y-4">
                <ButtonGroup>
                  <Button variant="outline">Previous</Button>
                  <Button>Current</Button>
                  <Button variant="outline">Next</Button>
                </ButtonGroup>
                
                <ButtonGroup attached>
                  <Button variant="outline">Day</Button>
                  <Button>Week</Button>
                  <Button variant="outline">Month</Button>
                </ButtonGroup>
              </div>
            </div>
          </Card>
        </section>

        {/* Card Showcase */}
        <section className="mb-12">
          <Card className="p-6 mb-6">
            <h2 className="text-2xl font-semibold mb-6">Enhanced Cards</h2>
            
            {/* Card Variants */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <Card variant="default">
                <CardContent>
                  <h4 className="font-medium mb-2">Default Card</h4>
                  <p className="text-sm text-gray-600">Standard card styling</p>
                </CardContent>
              </Card>
              
              <Card variant="elevated">
                <CardContent>
                  <h4 className="font-medium mb-2">Elevated Card</h4>
                  <p className="text-sm text-gray-600">Enhanced shadow</p>
                </CardContent>
              </Card>
              
              <Card variant="outlined">
                <CardContent>
                  <h4 className="font-medium mb-2">Outlined Card</h4>
                  <p className="text-sm text-gray-600">Prominent border</p>
                </CardContent>
              </Card>
              
              <Card variant="gradient">
                <CardContent>
                  <h4 className="font-medium mb-2">Gradient Card</h4>
                  <p className="text-sm text-gray-600">Gradient background</p>
                </CardContent>
              </Card>
            </div>
          </Card>

          {/* Recipe Cards Grid */}
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-semibold">Recipe Collection</h2>
              <div className="flex gap-2">
                <IconButton
                  icon={<Search className="w-4 h-4" />}
                  aria-label="Search recipes"
                  variant="outline"
                />
                <IconButton
                  icon={<Filter className="w-4 h-4" />}
                  aria-label="Filter recipes"
                  variant="outline"
                />
                <Button leftIcon={<Plus className="w-4 h-4" />}>
                  Add Recipe
                </Button>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {sampleRecipes.map((recipe) => (
                <Card key={recipe.id} interactive className="group">
                  <CardImage
                    src={recipe.image}
                    alt={recipe.title}
                    aspectRatio="video"
                  />
                  
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <CardTitle size="sm" className="truncate">
                          {recipe.title}
                        </CardTitle>
                        <CardDescription size="sm" className="line-clamp-2 overflow-hidden">
                          {recipe.description}
                        </CardDescription>
                      </div>
                      <IconButton
                        icon={
                          <Heart 
                            className={`w-4 h-4 transition-colors ${
                              likedRecipes.has(recipe.id) 
                                ? 'fill-red-500 text-red-500' 
                                : 'text-gray-400 hover:text-red-500'
                            }`} 
                          />
                        }
                        aria-label={
                          likedRecipes.has(recipe.id) 
                            ? 'Remove from favorites' 
                            : 'Add to favorites'
                        }
                        variant="ghost"
                        size="sm"
                        loading={loading === recipe.id}
                        onClick={() => toggleLike(recipe.id)}
                      />
                    </div>
                  </CardHeader>

                  <CardContent>
                    <div className="flex items-center gap-4 text-sm text-gray-600">
                      <div className="flex items-center gap-1">
                        <Clock className="w-4 h-4" />
                        <span>{recipe.time}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{recipe.servings}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{recipe.rating}</span>
                      </div>
                    </div>
                  </CardContent>

                  <CardFooter justify="between">
                    <ButtonGroup size="sm">
                      <Button variant="outline" size="sm">
                        <Bookmark className="w-4 h-4" />
                      </Button>
                      <Button size="sm">
                        <ChefHat className="w-4 h-4" />
                        Cook
                      </Button>
                    </ButtonGroup>
                  </CardFooter>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Interactive Demo */}
        <section className="mb-12">
          <Card className="p-6">
            <h2 className="text-2xl font-semibold mb-6">Interactive Features</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card variant="outlined" interactive>
                <CardContent className="text-center">
                  <ChefHat className="w-16 h-16 mx-auto mb-4 text-orange-500" />
                  <CardTitle>Start Cooking</CardTitle>
                  <CardDescription className="mb-4">
                    Begin your culinary journey with guided recipes
                  </CardDescription>
                  <Button fullWidth>Get Started</Button>
                </CardContent>
              </Card>

              <Card variant="gradient">
                <CardHeader>
                  <CardTitle>Kitchen Stats</CardTitle>
                  <CardDescription>Your cooking progress</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">
                        {sampleRecipes.length}
                      </div>
                      <div className="text-sm text-gray-600">Recipes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-orange-600">
                        {likedRecipes.size}
                      </div>
                      <div className="text-sm text-gray-600">Favorites</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </Card>
        </section>

        {/* Footer */}
        <Card className="p-6 text-center">
          <CardTitle className="mb-2">Design System Complete</CardTitle>
          <CardDescription className="mb-4">
            Enhanced components built with BMAD-METHOD for better UX and consistency
          </CardDescription>
          <ButtonGroup>
            <Button variant="outline">View Documentation</Button>
            <Button>Explore More</Button>
          </ButtonGroup>
        </Card>
      </div>
    </div>
  );
};

export default DesignSystemDemo;
